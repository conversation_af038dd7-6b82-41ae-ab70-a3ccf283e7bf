<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectView>ShowAllFiles</ProjectView>
    <PublishUrlHistory>C:\共享\Tmp\</PublishUrlHistory>
    <InstallUrlHistory />
    <SupportUrlHistory />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <StartAction>Program</StartAction>
    <StartArguments>
    </StartArguments>
    <StartProgram>C:\Users\<USER>\AppData\Local\Kingsoft\WPS Office\12.1.0.21541\office6\et.exe</StartProgram>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <RemoteDebugEnabled>false</RemoteDebugEnabled>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <StartProgram>C:\Users\<USER>\AppData\Local\kingsoft\WPS Office\ksolaunch.exe</StartProgram>
    <StartAction>Project</StartAction>
  </PropertyGroup>
</Project>